# 🏗️ Arquitetura Técnica SARAN

## 📋 **Visão Geral do Sistema**

### **Estrutura de Pastas**
```
saran/
├── client/                 # Frontend React
│   ├── src/
│   │   ├── components/     # Componentes reutilizáveis
│   │   ├── pages/         # Páginas da aplicação
│   │   ├── hooks/         # Custom hooks
│   │   ├── services/      # APIs e serviços
│   │   ├── utils/         # Utilitários
│   │   └── styles/        # Estilos globais
├── server/                # Backend Node.js
│   ├── controllers/       # Controladores
│   ├── models/           # Modelos de dados
│   ├── routes/           # Rotas da API
│   ├── services/         # Lógica de negócio
│   ├── middleware/       # Middlewares
│   └── utils/            # Utilitários
├── shared/               # Código compartilhado
└── docs/                 # Documentação
```

## 🎨 **Frontend - React + Shadcn/ui**

### **Páginas Principais**

#### **1. Landing Page (Suspense)**
- Hero section com animação
- Funcionalidades ocultas (reveal on scroll)
- Formulário de cadastro prévio
- Captcha integrado

#### **2. Dashboard**
- Visão geral do projeto
- Lista de propostas pendentes
- Status de aprovação
- Métricas de progresso

#### **3. Editor Colaborativo**
- Editor de texto rico
- Sincronização em tempo real
- Sistema de comentários
- Histórico de versões

#### **4. Modo Apresentação**
- Interface otimizada para TV
- Controles de navegação
- Sistema de votação
- Exibição de impactos

#### **5. Análise de Propostas**
- Lista de propostas por secretaria
- Status de implementação
- Análise de impacto
- Sugestões da IA

### **Componentes Principais**

```typescript
// Editor colaborativo
<CollaborativeEditor
  content={document}
  users={activeUsers}
  onSave={handleSave}
  onProposal={handleProposal}
/>

// Sistema de propostas
<ProposalTracker
  proposals={proposals}
  onApprove={handleApprove}
  onReject={handleReject}
  onComment={handleComment}
/>

// Análise de impacto
<ImpactAnalysis
  proposal={selectedProposal}
  conflicts={detectedConflicts}
  suggestions={aiSuggestions}
/>

// Modo apresentação
<PresentationMode
  document={currentDocument}
  proposals={activeProposals}
  voting={votingSystem}
/>
```

## ⚙️ **Backend - Node.js + Express**

### **APIs Principais**

#### **1. Autenticação**
```typescript
POST /api/auth/google
POST /api/auth/refresh
POST /api/auth/logout
```

#### **2. Documentos**
```typescript
GET /api/documents/:id
POST /api/documents
PUT /api/documents/:id
DELETE /api/documents/:id
GET /api/documents/:id/history
```

#### **3. Propostas**
```typescript
GET /api/proposals
POST /api/proposals
PUT /api/proposals/:id
DELETE /api/proposals/:id
POST /api/proposals/:id/vote
```

#### **4. Análise IA**
```typescript
POST /api/ai/analyze
POST /api/ai/suggest
POST /api/ai/translate
GET /api/ai/impact/:proposalId
```

#### **5. Exportação**
```typescript
POST /api/export/word
POST /api/export/pdf
POST /api/export/certificate
```

### **Modelos de Dados**

#### **Document**
```typescript
interface Document {
  id: string;
  title: string;
  content: string;
  version: number;
  status: 'draft' | 'review' | 'approved';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  participants: string[];
  history: DocumentVersion[];
}
```

#### **Proposal**
```typescript
interface Proposal {
  id: string;
  documentId: string;
  author: string;
  secretariat: string;
  type: 'addition' | 'modification' | 'deletion';
  content: string;
  originalText?: string;
  suggestedText?: string;
  status: 'pending' | 'approved' | 'rejected';
  votes: Vote[];
  comments: Comment[];
  impact: ImpactAnalysis;
  createdAt: Date;
}
```

#### **ImpactAnalysis**
```typescript
interface ImpactAnalysis {
  proposalId: string;
  affectedArticles: string[];
  budgetImpact: number;
  operationalImpact: 'low' | 'medium' | 'high';
  conflicts: Conflict[];
  suggestions: string[];
  timeline: TimelineItem[];
}
```

## 🔐 **Segurança**

### **Autenticação Google OAuth 2.0**
```typescript
// Configuração
const googleAuth = {
  clientId: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: '/api/auth/google/callback',
  scope: ['email', 'profile']
};

// Middleware de verificação
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Não autorizado' });
  }
  next();
};
```

### **reCAPTCHA v3**
```typescript
// Verificação no frontend
const verifyCaptcha = async (token: string) => {
  const response = await fetch('/api/verify-captcha', {
    method: 'POST',
    body: JSON.stringify({ token })
  });
  return response.json();
};
```

### **Rate Limiting**
```typescript
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // máximo 100 requests por IP
  message: 'Muitas requisições, tente novamente mais tarde'
});
```

## 🤖 **Integração IA - OpenAI**

### **Análise de Conformidade**
```typescript
const analyzeCompliance = async (text: string) => {
  const prompt = `
    Analise o seguinte texto de decreto municipal quanto à conformidade com:
    1. Constituição Federal
    2. Lei de Introdução às Normas do Direito Brasileiro
    3. Lei Complementar 95/1998 (Técnica Legislativa)
    4. Boas práticas de redação normativa
    
    Texto: ${text}
    
    Retorne um JSON com:
    - conformidade: boolean
    - issues: array de problemas encontrados
    - suggestions: array de sugestões
  `;
  
  return await openai.chat.completions.create({
    model: "gpt-4",
    messages: [{ role: "user", content: prompt }]
  });
};
```

### **Tradução de Propostas**
```typescript
const translateProposal = async (proposal: string) => {
  const prompt = `
    Traduza a seguinte proposta para linguagem jurídica técnica:
    
    Proposta: ${proposal}
    
    Retorne:
    - texto_tecnico: versão técnica da proposta
    - localizacao_sugerida: onde encaixar no documento
    - impactos: possíveis impactos
    - viabilidade: análise de viabilidade
  `;
  
  return await openai.chat.completions.create({
    model: "gpt-4",
    messages: [{ role: "user", content: prompt }]
  });
};
```

## 📡 **Comunicação em Tempo Real - Socket.io**

### **Eventos do Cliente**
```typescript
// Conectar ao editor
socket.emit('join-document', { documentId, userId });

// Enviar alteração
socket.emit('document-change', {
  documentId,
  userId,
  change: { position, text, type }
});

// Votar em proposta
socket.emit('vote-proposal', {
  proposalId,
  userId,
  vote: 'approve' | 'reject'
});
```

### **Eventos do Servidor**
```typescript
// Atualização do documento
socket.emit('document-updated', {
  documentId,
  change,
  author: userId
});

// Nova proposta
socket.emit('new-proposal', {
  proposal,
  author: userId
});

// Resultado da votação
socket.emit('vote-result', {
  proposalId,
  result: { approve: 5, reject: 2 }
});
```

## 📊 **Banco de Dados - MongoDB**

### **Coleções Principais**

#### **documents**
```javascript
{
  _id: ObjectId,
  title: String,
  content: String,
  version: Number,
  status: String,
  participants: [ObjectId],
  createdAt: Date,
  updatedAt: Date
}
```

#### **proposals**
```javascript
{
  _id: ObjectId,
  documentId: ObjectId,
  author: ObjectId,
  secretariat: String,
  type: String,
  content: String,
  status: String,
  votes: [{
    userId: ObjectId,
    vote: String,
    timestamp: Date
  }],
  createdAt: Date
}
```

#### **users**
```javascript
{
  _id: ObjectId,
  googleId: String,
  email: String,
  name: String,
  secretariat: String,
  role: String,
  createdAt: Date
}
```

## 🚀 **Deploy e Infraestrutura**

### **Ambiente de Desenvolvimento**
- **Frontend:** http://localhost:3000
- **Backend:** http://localhost:5000
- **MongoDB:** localhost:27017

### **Ambiente de Produção**
- **Frontend:** Vercel/Netlify
- **Backend:** Railway/Render
- **MongoDB:** MongoDB Atlas
- **CDN:** Cloudflare

### **Variáveis de Ambiente**
```env
# Google OAuth
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret

# OpenAI
OPENAI_API_KEY=your_openai_key

# MongoDB
MONGODB_URI=your_mongodb_uri

# reCAPTCHA
RECAPTCHA_SECRET_KEY=your_recaptcha_secret

# JWT
JWT_SECRET=your_jwt_secret
```

## 📈 **Monitoramento e Analytics**

### **Métricas Principais**
- Tempo de sessão por usuário
- Número de propostas por secretaria
- Taxa de aprovação de propostas
- Tempo médio de revisão
- Uso das funcionalidades de IA

### **Logs e Debugging**
- Winston para logs estruturados
- Sentry para monitoramento de erros
- Analytics do Google para comportamento do usuário

---

**Esta arquitetura garante escalabilidade, segurança e performance para o SARAN!** 🚀 