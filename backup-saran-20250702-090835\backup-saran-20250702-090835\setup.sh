#!/bin/bash

echo "🚀 Configurando SARAN..."

# Instalar dependências do backend
echo "📦 Instalando dependências do backend..."
npm install

# Instalar dependências do frontend
echo "📦 Instalando dependências do frontend..."
cd client
npm install
cd ..

# Criar arquivo .env se não existir
if [ ! -f .env ]; then
    echo "⚙️ Criando arquivo .env..."
    cat > .env << EOF
# Backend
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:4000

# JWT
JWT_SECRET=saran-secret-key-2024

# Outras variáveis (opcionais para desenvolvimento)
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
# OPENAI_API_KEY=your_openai_api_key
# MONGODB_URI=mongodb://localhost:27017/saran
# RECAPTCHA_SECRET_KEY=your_recaptcha_secret
EOF
    echo "✅ Arquivo .env criado!"
fi

echo "✅ Instalação concluída!"
echo ""
echo "🎯 Para executar o projeto:"
echo "   npm run dev"
echo ""
echo "📱 Acesse:"
echo "   Frontend: http://localhost:4000"
echo "   Backend:  http://localhost:5000"
echo "   Health:   http://localhost:5000/health" 