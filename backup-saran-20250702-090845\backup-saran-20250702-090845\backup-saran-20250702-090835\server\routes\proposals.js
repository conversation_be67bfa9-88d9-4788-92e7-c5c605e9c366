const express = require('express');
const router = express.Router();

// Get all proposals
router.get('/', async (req, res) => {
  try {
    // Mock proposals data
    const proposals = [
      {
        id: '1',
        documentId: '9337-24',
        author: {
          id: '1',
          name: '<PERSON>',
          secretariat: 'CLMP/SG',
          color: '#10B981'
        },
        type: 'addition',
        content: 'Incluir responsabilidade por fiscalização trimestral',
        originalText: '',
        suggestedText: 'V - Realizar fiscalização trimestral das atividades',
        status: 'pending',
        votes: [
          { userId: '1', vote: 'approve', timestamp: new Date().toISOString() },
          { userId: '2', vote: 'approve', timestamp: new Date().toISOString() }
        ],
        comments: [
          { userId: '3', text: 'Concordo com a proposta', timestamp: new Date().toISOString() }
        ],
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        documentId: '9337-24',
        author: {
          id: '2',
          name: '<PERSON>',
          secretariat: 'CLMP/SG',
          color: '#3B82F6'
        },
        type: 'modification',
        content: 'Adicionar prazo de 30 dias para prestação de contas',
        originalText: 'IV - Prestar contas de sua gestão.',
        suggestedText: 'IV - Prestar contas de sua gestão no prazo de 30 (trinta) dias.',
        status: 'approved',
        votes: [
          { userId: '1', vote: 'approve', timestamp: new Date().toISOString() },
          { userId: '2', vote: 'approve', timestamp: new Date().toISOString() },
          { userId: '3', vote: 'approve', timestamp: new Date().toISOString() }
        ],
        comments: [],
        createdAt: new Date().toISOString()
      }
    ];

    res.json(proposals);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create new proposal
router.post('/', async (req, res) => {
  try {
    const { documentId, author, type, content, originalText, suggestedText } = req.body;
    
    // TODO: Save to database
    const newProposal = {
      id: Date.now().toString(),
      documentId,
      author,
      type,
      content,
      originalText,
      suggestedText,
      status: 'pending',
      votes: [],
      comments: [],
      createdAt: new Date().toISOString()
    };

    res.status(201).json(newProposal);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Vote on proposal
router.post('/:id/vote', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, vote } = req.body;
    
    // TODO: Update proposal votes in database
    res.json({ 
      message: 'Vote recorded successfully',
      proposalId: id,
      vote
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add comment to proposal
router.post('/:id/comments', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, text } = req.body;
    
    // TODO: Add comment to database
    const comment = {
      userId,
      text,
      timestamp: new Date().toISOString()
    };

    res.json({ 
      message: 'Comment added successfully',
      comment
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 