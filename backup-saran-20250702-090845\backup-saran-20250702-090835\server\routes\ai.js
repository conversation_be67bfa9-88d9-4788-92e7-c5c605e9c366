const express = require('express');
const router = express.Router();

// Analyze text for compliance
router.post('/analyze', async (req, res) => {
  try {
    const { text } = req.body;
    
    // Mock AI analysis
    const analysis = {
      compliance: true,
      issues: [],
      suggestions: [
        'Considerar adicionar prazo específico',
        'Verificar competência da secretaria',
        'Incluir mecanismo de fiscalização'
      ],
      impact: {
        budget: 'low',
        operational: 'medium',
        affectedArticles: ['5', '8']
      }
    };

    res.json(analysis);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Translate proposal to legal language
router.post('/translate', async (req, res) => {
  try {
    const { proposal } = req.body;
    
    // Mock translation
    const translation = {
      technicalText: 'A Secretaria Municipal competente realizará fiscalização trimestral das atividades sob sua responsabilidade, conforme estabelecido em regulamento próprio.',
      suggestedLocation: 'Artigo 5º, inciso V',
      impacts: [
        'Impacto orçamentário: Baixo',
        'Impacto operacional: Médio',
        'Necessita dotação orçamentária'
      ],
      feasibility: 'Viável com recursos existentes'
    };

    res.json(translation);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get impact analysis for proposal
router.get('/impact/:proposalId', async (req, res) => {
  try {
    const { proposalId } = req.params;
    
    // Mock impact analysis
    const impact = {
      proposalId,
      affectedArticles: ['5', '8', '12'],
      budgetImpact: 15000,
      operationalImpact: 'medium',
      conflicts: [
        {
          type: 'competence',
          description: 'Conflito com competência da Secretaria de Obras',
          suggestion: 'Coordenar com Secretaria de Obras'
        }
      ],
      suggestions: [
        'Incluir dotação orçamentária específica',
        'Definir responsável pela execução',
        'Estabelecer cronograma de implementação'
      ],
      timeline: [
        {
          phase: 'Implementação',
          duration: '30 dias',
          responsible: 'Secretaria Executiva'
        }
      ]
    };

    res.json(impact);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 