const express = require('express');
const router = express.Router();

// Google OAuth login
router.post('/google', async (req, res) => {
  try {
    // TODO: Implement Google OAuth
    res.json({ 
      message: 'Google OAuth endpoint - to be implemented',
      user: {
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        secretariat: 'CLMP/SG',
        role: 'Analista',
        color: '#10B981'
      },
      token: 'mock-jwt-token'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Logout
router.post('/logout', (req, res) => {
  res.json({ message: 'Logged out successfully' });
});

// Verify token
router.get('/verify', (req, res) => {
  res.json({ 
    valid: true, 
    user: {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      secretariat: 'CLMP/SG',
      role: 'Ana<PERSON><PERSON>',
      color: '#10B981'
    }
  });
});

module.exports = router; 