import React from 'react';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-6">
            SARAN
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Sistema de Análise e Revisão de Atos Normativos
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-400 mb-12">
            Revolucionando a gestão de decretos, leis e atos normativos
          </p>
          
          <div className="space-y-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
              Solicitar Acesso
            </button>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Sistema em desenvolvimento - Acesso restrito
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage; 