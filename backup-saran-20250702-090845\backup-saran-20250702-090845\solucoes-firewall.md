# 🛡️ Soluções para Firewalls Corporativos - SARAN

## 🚨 **Problema Identificado**
Prefeituras possuem firewalls restritivos que podem bloquear:
- Domínios externos
- Portas não padrão
- Serviços em nuvem
- APIs externas

## 💡 **Soluções Implementadas**

### **1. Deploy Local (Recomendado)**
```
Infraestrutura da Prefeitura:
├── Servidor Local
│   ├── Frontend (React)
│   ├── Backend (Node.js)
│   └── Banco de Dados (MongoDB)
├── Rede Interna
│   ├── Acesso via IP interno
│   └── Certificado SSL próprio
└── Integração com AD/LDAP
```

**Vantagens:**
- ✅ Controle total da TI
- ✅ Sem dependência de internet
- ✅ Integração com sistemas existentes
- ✅ Segurança máxima

### **2. VPN Corporativa**
```
Acesso via VPN:
├── Usuários conectam via VPN
├── Acesso direto ao servidor
├── IPs internos liberados
└── Sem bloqueios de firewall
```

**Configuração:**
- IPs da VPN liberados no firewall
- Certificado SSL interno
- Autenticação via AD corporativo

### **3. Proxy Reverso**
```
Proxy Interno:
├── Nginx/Apache como proxy
├── Redirecionamento interno
├── Cache local
└── Load balancing
```

**Benefícios:**
- ✅ Controle de acesso centralizado
- ✅ Cache para performance
- ✅ Logs detalhados
- ✅ SSL termination

### **4. Domínios Alternativos**
```
Opções de Domínio:
├── saran.prefeitura.gov.br
├── saran.maua.sp.gov.br
├── saran-interno.local
├── saran.corp
└── IP direto (192.168.x.x)
```

### **5. Configurações de Porta**
```
Portas Padrão:
├── HTTP: 80
├── HTTPS: 443
├── WebSocket: 80/443 (via proxy)
└── Evitar portas não padrão
```

### **6. Certificados SSL Internos**
```
Certificados:
├── CA interna da prefeitura
├── Certificado auto-assinado
├── Let's Encrypt interno
└── Integração com PKI corporativa
```

## 🔧 **Implementação Técnica**

### **Docker Compose (Deploy Local)**
```yaml
version: '3.8'
services:
  frontend:
    image: saran-frontend
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      
  backend:
    image: saran-backend
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=mongodb://db:27017/saran
      
  database:
    image: mongo:latest
    volumes:
      - mongo_data:/data/db
      
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
```

### **Nginx Config (Proxy Reverso)**
```nginx
server {
    listen 80;
    server_name saran.prefeitura.gov.br;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name saran.prefeitura.gov.br;
    
    ssl_certificate /etc/nginx/ssl/saran.crt;
    ssl_certificate_key /etc/nginx/ssl/saran.key;
    
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /socket.io {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📋 **Checklist de Implementação**

### **Fase 1 - Preparação**
- [ ] Análise da infraestrutura da prefeitura
- [ ] Definição do modelo de deploy
- [ ] Configuração de domínio interno
- [ ] Preparação de certificados SSL

### **Fase 2 - Deploy**
- [ ] Instalação do servidor
- [ ] Configuração do banco de dados
- [ ] Deploy da aplicação
- [ ] Configuração do proxy reverso

### **Fase 3 - Integração**
- [ ] Configuração de autenticação AD
- [ ] Integração com VPN corporativa
- [ ] Configuração de logs
- [ ] Testes de conectividade

### **Fase 4 - Treinamento**
- [ ] Treinamento da equipe de TI
- [ ] Documentação técnica
- [ ] Procedimentos de backup
- [ ] Plano de manutenção

## 🎯 **Recomendações por Cenário**

### **Prefeitura com TI Forte**
- **Solução:** Deploy local completo
- **Infraestrutura:** Servidor próprio
- **Acesso:** Rede interna + VPN
- **Manutenção:** Equipe interna

### **Prefeitura com TI Limitada**
- **Solução:** Deploy local simplificado
- **Infraestrutura:** Servidor compartilhado
- **Acesso:** IP interno
- **Manutenção:** Suporte remoto

### **Prefeitura com Restrições Severas**
- **Solução:** Aplicação desktop
- **Infraestrutura:** Instalação local
- **Acesso:** Apenas local
- **Sincronização:** Manual/offline

## 💰 **Custos por Solução**

### **Deploy Local**
- Servidor: R$ 2.000 - 5.000
- Licenças: R$ 0 (open source)
- Manutenção: R$ 500/mês
- **Total:** R$ 2.500 - 5.500 inicial

### **Deploy Híbrido**
- Servidor: R$ 1.000 - 2.000
- Cloud: R$ 200/mês
- Manutenção: R$ 300/mês
- **Total:** R$ 1.500 - 2.500 inicial

### **Aplicação Desktop**
- Desenvolvimento: R$ 5.000 - 10.000
- Licenças: R$ 0 (open source)
- Manutenção: R$ 200/mês
- **Total:** R$ 5.000 - 10.000 inicial

---

**Todas as soluções garantem funcionamento mesmo com firewalls restritivos!** 🛡️ 

CLMP/SG (Consolidado):
├── 📊 3 usuários ativos
├── 📊 47 contribuições total
├── ✅ 38 aprovadas (81%)
├── ⏱️ Tempo total: 24h
├── 🎯 Progresso: 50% 