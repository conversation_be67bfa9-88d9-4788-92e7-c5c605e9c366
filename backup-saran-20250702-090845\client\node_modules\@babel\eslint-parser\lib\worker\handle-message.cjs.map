{"version": 3, "names": ["babel", "require", "<PERSON><PERSON><PERSON><PERSON>", "maybeParseSync", "astInfo", "config", "Clients", "ACTIONS", "module", "exports", "handleMessage", "action", "payload", "GET_VERSION", "version", "GET_TYPES_INFO", "FLOW_FLIPPED_ALIAS_KEYS", "types", "FLIPPED_ALIAS_KEYS", "Flow", "VISITOR_KEYS", "GET_TOKEN_LABELS", "getTokLabels", "GET_VISITOR_KEYS", "getVisitorKeys", "MAYBE_PARSE", "normalizeBabelParseConfig", "options", "then", "code", "MAYBE_PARSE_SYNC", "normalizeBabelParseConfigSync", "Error"], "sources": ["../../src/worker/handle-message.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport maybeParse = require(\"./maybeParse.cts\");\nimport maybeParseSync = require(\"./maybeParseSync.cts\");\nimport astInfo = require(\"./ast-info.cts\");\nimport config = require(\"./configuration.cts\");\n\nimport Clients = require(\"../client.cts\");\nimport ACTIONS = Clients.ACTIONS;\n\nexport = function handleMessage(action: ACTIONS, payload: any) {\n  switch (action) {\n    case ACTIONS.GET_VERSION:\n      return babel.version;\n    case ACTIONS.GET_TYPES_INFO:\n      return {\n        FLOW_FLIPPED_ALIAS_KEYS: babel.types.FLIPPED_ALIAS_KEYS.Flow,\n        VISITOR_KEYS: babel.types.VISITOR_KEYS,\n      };\n    case ACTIONS.GET_TOKEN_LABELS:\n      return astInfo.getTokLabels();\n    case ACTIONS.GET_VISITOR_KEYS:\n      return astInfo.getVisitorKeys();\n    case ACTIONS.MAYBE_PARSE:\n      return config\n        .normalizeBabelParseConfig(payload.options)\n        .then(options => maybeParse(payload.code, options));\n    case ACTIONS.MAYBE_PARSE_SYNC:\n      if (!USE_ESM) {\n        return maybeParseSync(\n          payload.code,\n          config.normalizeBabelParseConfigSync(payload.options),\n        );\n      }\n  }\n\n  throw new Error(`Unknown internal parser worker action: ${action}`);\n};\n"], "mappings": ";;MAAOA,KAAK,GAAAC,OAAA,CAAW,kBAAkB;AAAA,MAClCC,UAAU,GAAAD,OAAA,CAAW,kBAAkB;AAAA,MACvCE,cAAc,GAAAF,OAAA,CAAW,sBAAsB;AAAA,MAC/CG,OAAO,GAAAH,OAAA,CAAW,gBAAgB;AAAA,MAClCI,MAAM,GAAAJ,OAAA,CAAW,qBAAqB;AAAA,MAEtCK,OAAO,GAAAL,OAAA,CAAW,eAAe;AAAA,IACjCM,OAAO,GAAGD,OAAO,CAACC,OAAO;AAAAC,MAAA,CAAAC,OAAA,GAEvB,SAASC,aAAaA,CAACC,MAAe,EAAEC,OAAY,EAAE;EAC7D,QAAQD,MAAM;IACZ,KAAKJ,OAAO,CAACM,WAAW;MACtB,OAAOb,KAAK,CAACc,OAAO;IACtB,KAAKP,OAAO,CAACQ,cAAc;MACzB,OAAO;QACLC,uBAAuB,EAAEhB,KAAK,CAACiB,KAAK,CAACC,kBAAkB,CAACC,IAAI;QAC5DC,YAAY,EAAEpB,KAAK,CAACiB,KAAK,CAACG;MAC5B,CAAC;IACH,KAAKb,OAAO,CAACc,gBAAgB;MAC3B,OAAOjB,OAAO,CAACkB,YAAY,CAAC,CAAC;IAC/B,KAAKf,OAAO,CAACgB,gBAAgB;MAC3B,OAAOnB,OAAO,CAACoB,cAAc,CAAC,CAAC;IACjC,KAAKjB,OAAO,CAACkB,WAAW;MACtB,OAAOpB,MAAM,CACVqB,yBAAyB,CAACd,OAAO,CAACe,OAAO,CAAC,CAC1CC,IAAI,CAACD,OAAO,IAAIzB,UAAU,CAACU,OAAO,CAACiB,IAAI,EAAEF,OAAO,CAAC,CAAC;IACvD,KAAKpB,OAAO,CAACuB,gBAAgB;MACb;QACZ,OAAO3B,cAAc,CACnBS,OAAO,CAACiB,IAAI,EACZxB,MAAM,CAAC0B,6BAA6B,CAACnB,OAAO,CAACe,OAAO,CACtD,CAAC;MACH;EACJ;EAEA,MAAM,IAAIK,KAAK,CAAC,0CAA0CrB,MAAM,EAAE,CAAC;AACrE,CAAC", "ignoreList": []}