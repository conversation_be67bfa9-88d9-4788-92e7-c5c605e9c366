const express = require('express');
const router = express.Router();

// Get document by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Mock document data
    const document = {
      id,
      title: 'Decreto 9337/24 - Mauá',
      content: `
        DECRETO Nº 9.337, DE 15 DE JANEIRO DE 2024

        Dispõe sobre a organização administrativa da Prefeitura Municipal de Mauá.

        O PREFEITO MUNICIPAL DE MAUÁ, no uso de suas atribuições legais,

        DECRETA:

        Art. 1º - Este Decreto dispõe sobre a organização administrativa da Prefeitura Municipal de Mauá.

        Art. 2º - A administração municipal será exercida pelos seguintes órgãos:
        I - Gabinete do Prefeito;
        II - Secretarias Municipais;
        III - Autarquias;
        IV - Fundações;
        V - Empresas Públicas.

        Art. 3º - As Secretarias Municipais são órgãos de execução das políticas públicas municipais.

        Art. 4º - Cada Secretaria será dirigida por um Secretário Municipal, nomeado pelo Prefeito.

        Art. 5º - Compete às Secretarias Municipais:
        I - Executar as políticas públicas de sua área de atuação;
        II - Coordenar as atividades de suas unidades administrativas;
        III - Elaborar e executar o orçamento de sua pasta;
        IV - Prestar contas de sua gestão.

        Parágrafo único. As competências específicas de cada Secretaria serão definidas em ato do Prefeito.

        Art. 6º - As Autarquias são entidades da administração indireta, dotadas de personalidade jurídica própria.

        Art. 7º - As Fundações são entidades da administração indireta, constituídas sob a forma de pessoa jurídica de direito privado.

        Art. 8º - As Empresas Públicas são entidades da administração indireta, constituídas sob a forma de sociedade de economia mista.

        Art. 9º - O controle interno da administração municipal será exercido pela Controladoria Geral do Município.

        Art. 10º - Este Decreto entra em vigor na data de sua publicação.

        Prefeitura Municipal de Mauá, 15 de janeiro de 2024.
      `,
      version: 1,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      participants: [
        {
          id: '1',
          name: 'João Silva',
          secretariat: 'CLMP/SG',
          color: '#10B981'
        },
        {
          id: '2',
          name: 'Maria Santos',
          secretariat: 'CLMP/SG',
          color: '#3B82F6'
        },
        {
          id: '3',
          name: 'Ana Oliveira',
          secretariat: 'SF',
          color: '#EF4444'
        }
      ]
    };

    res.json(document);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update document
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, version } = req.body;
    
    // TODO: Save to database
    res.json({ 
      message: 'Document updated successfully',
      id,
      version: version + 1
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get document history
router.get('/:id/history', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Mock history data
    const history = [
      {
        version: 1,
        content: 'Initial version',
        author: 'João Silva',
        timestamp: new Date().toISOString()
      }
    ];

    res.json(history);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 