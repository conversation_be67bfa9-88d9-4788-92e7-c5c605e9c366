# 🚀 SARAN - Sistema de Análise e Revisão de Atos Normativos

Sistema revolucionário para análise colaborativa de decretos, leis e atos normativos municipais.

## 📋 **Funcionalidades Principais**

- ✅ **Editor Colaborativo em Tempo Real**
- ✅ **Sistema de Propostas com Cores por Usuário**
- ✅ **Análise de Impacto com IA**
- ✅ **Modo Apresentação para Reuniões**
- ✅ **Dashboard com Métricas Visuais**
- ✅ **Sistema de Layers para Contribuições**
- ✅ **Mobile Responsive + Dark Mode**
- ✅ **Autenticação Google OAuth**

## 🛠️ **Tecnologias**

### **Frontend**
- React 18 + TypeScript
- Shadcn/ui + Tailwind CSS
- Socket.io (tempo real)
- React Query (estado)
- Framer Motion (animações)

### **Backend**
- Node.js + Express
- Socket.io (sincronização)
- OpenAI API (análise IA)
- JWT (autenticação)
- MongoDB (banco de dados)

## 🚀 **Instalação e Execução**

### **Pré-requisitos**
- Node.js 16+
- npm ou yarn

### **1. Instalar Dependências**
```bash
# Instalar dependências do backend
npm install

# Instalar dependências do frontend
cd client
npm install
cd ..
```

### **2. Configurar Variáveis de Ambiente**
Criar arquivo `.env` na raiz do projeto:
```env
# Backend
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:4000

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# MongoDB
MONGODB_URI=your_mongodb_uri

# JWT
JWT_SECRET=your_jwt_secret

# reCAPTCHA
RECAPTCHA_SECRET_KEY=your_recaptcha_secret
```

### **3. Executar o Projeto**
```bash
# Executar backend e frontend simultaneamente
npm run dev

# Ou executar separadamente:
# Backend
npm run server

# Frontend
cd client && npm start
```

## 📱 **Acesso ao Sistema**

- **Frontend:** http://localhost:4000
- **Backend:** http://localhost:5000
- **Health Check:** http://localhost:5000/health

## 🎯 **Estrutura do Projeto**

```
saran/
├── client/                 # Frontend React
│   ├── src/
│   │   ├── components/     # Componentes UI
│   │   ├── pages/         # Páginas
│   │   ├── stores/        # Zustand stores
│   │   └── styles/        # Estilos
│   └── public/            # Arquivos públicos
├── server/                # Backend Node.js
│   ├── routes/           # Rotas da API
│   ├── controllers/      # Controladores
│   ├── models/          # Modelos
│   └── middleware/      # Middlewares
├── docs/                 # Documentação
└── README.md
```

## 🔧 **Funcionalidades Implementadas**

### **MVP (Fase 1)**
- ✅ Estrutura base do projeto
- ✅ Landing page com suspense
- ✅ Sistema de autenticação básico
- ✅ Rotas protegidas
- ✅ API REST básica
- ✅ Mock de dados para testes

### **Próximas Fases**
- 🔄 Editor colaborativo em tempo real
- 🔄 Sistema de propostas com cores
- 🔄 Integração com IA
- 🔄 Modo apresentação
- 🔄 Dashboard com métricas

## 🎨 **Sistema de Cores por Usuário**

Cada usuário tem uma cor única para identificação visual:

- 🟢 **João Silva (CLMP/SG)** = Verde
- 🔵 **Maria Santos (CLMP/SG)** = Azul
- 🟠 **Pedro Costa (CLMP/SG)** = Laranja
- 🔴 **Ana Oliveira (SF)** = Vermelho
- 🟣 **Carlos Lima (SF)** = Roxo
- 🟡 **Paula Ferreira (SAJ)** = Amarelo
- 🟤 **Roberto Alves (SAJ)** = Marrom

## 📊 **Dashboard e Métricas**

- Gráficos de contribuições por usuário
- Consolidação por secretaria
- Status de artigos em cores
- Ranking de participação
- Alertas visuais de conflitos

## 🛡️ **Segurança**

- Autenticação Google OAuth 2.0
- reCAPTCHA anti-robô
- Rate limiting
- Headers de segurança
- Criptografia SSL/TLS

## 📈 **Roadmap**

### **Fase 1 - MVP (4 semanas)** ✅
- Estrutura base
- Autenticação básica
- Mock de dados

### **Fase 2 - Editor (6 semanas)** 🔄
- Editor colaborativo
- Sistema de propostas
- Cores por usuário

### **Fase 3 - IA (4 semanas)** 📋
- Integração OpenAI
- Análise de impacto
- Sugestões automáticas

### **Fase 4 - Apresentação (2 semanas)** 📋
- Modo apresentação
- Dashboard completo
- Export de documentos

## 🤝 **Contribuição**

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 **Licença**

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

---

**SARAN: Revolucionando a Gestão de Atos Normativos no Brasil** 🇧🇷 